# 逻辑回归 Logistic Regression
输出 Y=1 的对数几率是由输入 x 的**线性函数表示**的模型，这就是**逻辑回归模型**。

因此逻辑回归的思路是，先拟合决策边界(不局限于线性，还可以是多项式)，再建立这个边界与分类的概率联系，从而得到了二分类情况下的概率。

从概率上讲，一件事发生的概率为$p$的话，那么不发生的概率就是$1-p$，对于样本而言，属于第一类的概率符合这个理论，只不过这里的概率非0即1。我们定义这件事发生的几率，并给出表达
$$
Logit(p) = \frac{p}{1-p}
$$

$$
\phi(z) = \frac{1}{1 + e^z}
$$
称之为$sigmoid$函数

在线性感知器中，我们通过梯度下降算法来使得预测值与实际值的误差的平方和最小，来求得权重和阈值。而在逻辑斯蒂回归中所定义的代价函数就是使得该件事情发生的几率最大，也就是某个样本属于其真实标记样本的概率越大越好。这里我们使用梯度上升。

我们有最大似然函数
$$
L(\omega) = P(y|x;\omega) = \prod_{i=1}^{n}P(y^{(i)}|x^{(i)};\omega) = (\phi(z^{(i)}))^{y^{(i)}}(1 - \phi(z^{(i)}))^{1-y^{(i)}}
$$
为了防止溢出取对数
$$
l(\omega) = L(\omega) = P(y|x;\omega) = \sum_{i=1}^{n}(y^{(i)}log(\phi(z^{(i)})) + (1-y^{(i)})log(1-\phi(z^{(i)}))
$$
为了判断是否足够收敛，我们定义$loss$
$$
J(\phi(z), y;\omega) = 
\left\{
\begin{array}{ll}
log(\phi(z)), & y=1,\\
log(1-\phi(z)), & y=0. \\
\end{array}
\right.
$$
式$(5)$与式$(6)$相等。

在进行了最初的计算之后我们需要对权重$W$进行更新。

我们对似然函数求偏导数得到
$$
\frac{\partial}{\partial\omega_j}l(\omega) = (y - \phi(z))x_j
$$
这便是梯度，更新后的$W$为
$$
W = W - \frac{1}{m}(Data^T \cdot (Result - \phi(z)))
$$
添加正则化惩罚之后变为
$$
J(\omega) = l(\omega) + \frac{\lambda}{2m}||\omega||_2^2 \\
W = W - \frac{1}{m}(Data^T \cdot (Result - \phi(z))) - \frac{\lambda}{m}W
$$
以上所有均适用于
$$
z = X^T \cdot W
$$


# 支持向量机 SVM
支持向量机（Support Vector Machine, SVM）是一类按监督学习方式对数据进行二元分类的广义线性分类器，其决策边界是对学习样本求解的**最大边距超平面**。

![](https://img-blog.csdnimg.cn/20201118110703787.png#pic_center)
## 函数间隔 & 几何间隔
通过观察$\omega^Tx+b$的符号与类标记y的符号是否一致可判断分类是否正确，即用$y(\omega^Tx+b)$的正负性来判定或表示分类的正确性
定义**函数间隔**为$$\hat{\gamma}=y(\omega^Tx+b)=yf(x)$$

事实上，我们可以对法向量w加些约束条件，从而引出真正定义点到超平面的距离：**几何间隔**
$$\gamma = \frac{\omega^Tx+b}{||\omega||}$$

## 最大间隔
令$\gamma$最大，相当于最大化$\frac{1}{||\omega||}$，相当于最小化$\frac{1}{2}{||\omega||}^2$。

条件为$y_i(\omega^Tx_i+b)\ge1, \forall i\in[1, n]$，即标记为-1的点要在$\omega^Tx+b=-1$以外，反之标记为1的点要在$\omega^Tx+b=1$以外。

![](https://img-blog.csdnimg.cn/20201118125731701.png#pic_center)
## 松弛变量
解决过拟合的办法是为SVM引入了松弛变量$ξ$（slack variable），将SVM公式的约束条件改为：
$$y_i(\omega^Tx_i+b)\ge1-\xi_i, \forall i\in[1, n]$$



![](https://img-blog.csdnimg.cn/20201118130119761.png#pic_center)
> 从图可以看到，引入松弛变量使SVM能够容忍异常点的存在。为什么？因为引入松弛变量后，所有点到超平面的距离约束不需要大于等于1了，而是大于0.8就行了（如果ξ=0.2的话），那么异常点就可以不是支持向量了，它就作为一个普通的点存在，我们的支持向量和超平面都不会受到它的影响。

如果我们运行$\xi_i$任意大的话，那任意的超平面都是符合条件的了。所以，我们在原来的目标函数后面加上一项，使得这些$\xi_i$的总和也要最小：
$$\min(\frac{1}{2}{||\omega||}^2+C\sum_{i=1}^n{\xi_i})$$
其中$C$是一个参数，用于控制目标函数中两项（“寻找 margin 最大的超平面”和“保证数据点偏差量最小”）之间的权重。注意，其中$\xi_i$是需要优化的变量（之一），而$C$是一个事先确定好的常量。

## 核函数
> 特征空间的隐式映射：**Kernel**

在线性不可分的情况下，支持向量机首先在低维空间中完成计算，然后通过核函数将输入空间映射到高维特征空间，最终在高维特征空间中构造出最优分离超平面，从而把平面上本身不好分的非线性数据分开。如图所示，一堆数据在二维空间无法划分，从而映射到三维空间里划分：

![](https://img-blog.csdnimg.cn/20201118152043555.png#pic_center)
> 建立非线性学习器分为两步：
> 1. 首先使用一个非线性映射将数据变换到一个特征空间F
> 2. 然后在特征空间使用线性学习器分类

计算两个向量在隐式映射过后的空间中的内积的函数叫做核函数 (Kernel Function) 

1. 实际中，我们会经常遇到线性不可分的样例，此时，我们的常用做法是把样例特征映射到高维空间中去
2. 但进一步，如果凡是遇到线性不可分的样例，一律映射到高维空间，那么这个维度大小是会太高
3. 核函数的价值在于它虽然也是将特征进行从低维到高维的转换，但核函数绝就绝在它事先在低维上进行计算，而将实质上的分类效果表现在了高维上，也就如上文所说的避免了直接在高维空间中的复杂计算。