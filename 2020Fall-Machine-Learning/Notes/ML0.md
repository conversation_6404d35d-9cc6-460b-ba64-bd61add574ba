# 机器学习

- 概念
- 应用
- 分类

# 决策树

- 表示
- 构造
- 对属性的区分
- 确定“最好”的切分，衡量属性的“好”与“坏”
	- 样本熵
	- 信息增益
	- 终止准则
- 优点
- 欠拟合 & 过拟合

# 曲线拟合 / 回归问题

- Lab1

# 概率

- 随机变量
- 连续型概率分布
	- Normal (Gaussian) Probability Density Function
		- pdf
		- cdf
	- Exponential Probability Distribution
- 独立
- 条件概率
- 先验概率、后验概率
- 概率推理
- 贝叶斯定理
- 联合概率、边缘概率

# 概率

> 参数学习：从数据中学习参数

- 例1：伯努利模型
- 例2：单变量正态

- 贝叶斯学习
	- 基于伯努利的贝叶斯估计
- MLE & MAP / Frequentist & Bayesian
- 狄利克雷分布

# 分类

- 最优分类器
- 线性判别
- 生成式模型与判别式模型
- 
- 分割高维空间
- 贝叶斯决策定理
- 贝叶斯误差
- 

# KNN

> Instance-Based Learning

- 算法流程
- K值选择
- 影响因素

# 贝叶斯分类

> learn classifiers by learning P(Y|X)  

- 贝叶斯法则
- 条件独立
- 朴素贝叶斯公式、算法
- 





