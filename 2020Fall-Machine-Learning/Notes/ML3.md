# 贝叶斯分类器
贝叶斯分类器是各种分类器中分类错误概率最小或者在预先给定代价的情况下平均风险最小的分类器。它的设计方法是一种最基本的统计分类方法。其分类原理是通过某对象的先验概率，利用贝叶斯公式计算出其后验概率，即该对象属于某一类的概率，选择具有最大后验概率的类作为该对象所属的类。

如果密度估计值收敛到真密度，则分类器成为贝叶斯分类器
- 使用无穷样本
- 由此产生的误差是贝叶斯误差，即给定基本分布的最小可实现误差
# 贝叶斯判别
-  用贝叶斯决策理论分类要事先知道**两个条件及要求**：
	- 各类的先验概率$P(w_i)$及特征向量的条件概率密度：$p(x|w_i)$（或后验概率：$P(w_i|x)$）
	- 决策分类的类别一定

-  解决的**问题**：
已知一定数目的样本，设计分类器，对未知样本进行分类。

- 基于样本的两步贝叶斯决策
	- 首先**根据样本估计**$P(w_i)$和$p(x|w_i)$记为$\widehat{P}(w_i)$和$\widehat{p}(x|w_i)$
	- 然后用估计的概率密度设计贝叶斯分类器

> 前提：训练样本的分布能代表样本的真实分布。每个样本集中的样本都是所谓**独立同分布的随机变量**，且有充分的训练样本
>假设：当样本数N →∞时，如此得到的分类器收敛于理论上的最优解。 即满足：$\widehat{P}(w_i)→P(w_i)，\widehat{p}(x|w_i)→p(x|w_i)$

- 先验概率与条件概率密度估计
	- 类的先验概率估计：可依靠经验或训练数据中各类出现的频率估计，容易实现；
	- 类条件概率密度的估计：概率密度函数包含了一个随机变量的全部信息，估计起来比较困难。

- **概率密度估计**的两种基本方法
	- **参数估计**：根据对问题的一般性的认识，假设随机变量服从某种分布，分布函数的参数通过训练数据来估计
		- 如：ML 估计，Bayesian估计
	- 非参数估计：不依赖于与潜在密度函数结构有关的任何假设
		- 如：Parzen窗法，kn-近邻估计法。

# 贝叶斯估计
可以把概率密度函数的参数估计问题看作一个贝叶斯决策问题，但这里决策的不是离散类别，而是参数的值，是在连续空间里做的决策。在用于分类的贝叶斯决策中，最优的条件可以是最小错误率或者最小风险。对连续变量：$\theta$，我们假定把它估计为：$\widehat\theta$,所带来的损失函数为：$\lambda(\widehat\theta,\theta)$
定义在样本x下的条件风险为：$$R(\widehat\theta|x)=\int_{\Theta}{\lambda(\widehat\theta,\theta)}p(\theta|x)d\theta$$

则估计时总期望风险为：$$R=\int_{E^d}{R(\widehat\theta|x)}p(x)dx$$

现在的目标是对期望风险求最小，而条件风险都是非负的，求期望风险最小就等价于对所有可能的x求条件风险最小。在有限样本集合的情况下，我们所作的就是对所有的样本求条件风险最小，即：$$\theta^*=arg \space \min_{\widehat\theta}\space R(\widehat\theta|\chi)=\int_{\Theta}{\lambda(\widehat\theta,\theta)}p(\theta|\chi)d\theta$$

在决策分类时，需要事先定义决策表即损失表，连续情况下需要定义损失函数，**最常用的损失函数是平方误差损失函数**，即：$$\lambda(\widehat\theta,\theta)=(\theta-\widehat\theta)^2$$

可以证明，如果采用平方误差损失函数，则θ 的贝叶斯估计量θ*是在给定x 时θ 的条件期望，即：$$\theta^*=E[\theta|x]=\int_{\Theta}{\theta}p(\theta|x)d\theta$$

在许多情况下，**最小方差贝叶斯估计是最理想的，是贝叶斯的最优估计**。

# 贝叶斯误差
贝叶斯误差（bayes error rate)是指在现有特征集上，任意可以基于特征输入进行随机输出的分类器所能达到最小误差。也可以叫做最小误差。
$$r(X)=\min[q_1(X), q_2(X)]$$
$$\begin{aligned}
\epsilon&=E(r(X))=\int r(x)p(x)dx \\
& =\int \min[\pi_1p_1(x), \pi_2p_2(x)]dx \\
& =\pi_1\int_{L_1}p_1(x)dx+\pi_2\int_{L_2}p_2(x)dx\\
&=\pi_1\epsilon_1+\pi_2\epsilon_2
 \end{aligned}$$
![](https://img-blog.csdnimg.cn/20201115200350940.png#pic_center)
# 分类决策
$$h(X)=-\ln p_1(X) + \ln p_2(X) ^{>}_{<} \ln{\frac{\pi_1}{\pi_2}}$$


# 判别式模型 & 生成式模型
- 判别式模型（Discriminative Model）是直接对条件概率$p(y|x;θ)$建模。
常见的判别式模型有：线性回归、决策树、支持向量机SVM、KNN、神经网络等；
- 生成式模型（Generative Model）则会对x和y的联合分布$p(x,y)$建模，然后通过贝叶斯公式来求得$p(y_i|x)$，然后选取使得$p(y_i|x)$最大的yi，即：
$$\arg \underset{y}{\max}p(y|x) = \arg \underset{y}{\max}\frac{p(x|y)p(y)}{p(x)}$$
如隐马尔可夫模型HMM、朴素贝叶斯模型、高斯混合模型GMM、LDA等；

> **模型不确定性**是由模型在测试期间看到的数据与用于训练模型的数据之间的分布不匹配导致的。

# KNN
> Instance-Based Learning

KNN分类算法包括以下4个步骤：
1. 准备数据，对数据进行预处理。
2. 计算测试样本点（也就是待分类点）**到其他每个样本点的距离**。
3. **对每个距离进行排序**，然后选择出距离最小的K个点。
4. 对K个点所属的类别进行比较，根据少数服从多数的原则，将测试样本点归入在K个点中占比最高的那一类。

## 优点
KNN方法思路简单，易于理解，易于实现，无需估计参数，无需训练。
## 缺点
该算法在分类时有个主要的不足是，当**样本不平衡**时，如一个类的样本容量很大，而其他类样本容量很小时，有可能导致当输入一个新样本时，该样本的K个邻居中大容量类的样本占多数 。
该方法的另一个不足之处是**计算量较大**，因为对每一个待分类的文本都要计算它到全体已知样本的距离，才能求得它的K个最近邻点。

# 线性分类器
线性分类器使用线性的函数表达式对样本进行分类，即划分边界为一个超平面，如：在二维空间中使用一条直线划分样本，在三维空间中使用一个平面来划分样本。

