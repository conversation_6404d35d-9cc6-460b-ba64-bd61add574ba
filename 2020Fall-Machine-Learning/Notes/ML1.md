# 决策树

## 表示

- 输入：属性向量
- 输出：决策Y
- 每一个内结点：测试一个属性Xi
- 每个分支： 选择属性Xi的一个取值
- 每个叶结点：预测Y
![](https://img-blog.csdnimg.cn/20201113102828401.png#pic_center)

> 同样一个训练数据集，可以有多棵树与其一致

## 构造
> Top-Down的决策树归纳算法

Main loop：
1. A←下一个结点node的**最好属性**
2. 把A作为决策属性赋给结点node
3. 对A的每一个取值，创建一个新的儿子结点node
4. 把相应的训练样本分到叶结点
5. 如果训练样本被很好的分类，则停止，否则在新的叶结点上重复上述过程

> 如何确定特测条件？

依赖于属性类型
- 名词性\离散（ Nominal）
- 有序的（ Ordinal）
- 连续（ Continuous）

依赖于切分的分支个数
- 两路切分（ 2-way）
- 多路切分（ Multi-way）

### 如何确定最好的切分
- 最理想情况是每个子集为“皆为正例”或“皆为反例”
- 更倾向结点上的数据具有**同质homogeneous**类别分布
- 需要对**结点混杂度impurity**进行测量

### 如何衡量属性的“好”与“坏”
**熵Entropy**
- $H(X)$是对从$X$随机采样值在最短编码情况下的每个值平均
（期望）长度（以2为底就是0、1编码）
$$H(X)=-\Sigma^N_{i=1}P(x=i)\log_{2}P(x=i)$$
> 在最短编码情况下，对消息$x=i$分配$-\log_{2}P(x=i)$位，所以其编码一个随机变量𝑋𝑋的期望位数即$H(X)$

特定条件熵：y为某一取值时X的熵𝐻(𝑋|𝑌 = 𝑣)
条件熵：给定Y时X的熵𝐻(𝑋|𝑌)
互信息：$I(X; Y) = H(X) - H(X|Y) = H(Y) - H(Y|X) = H(X) + H(Y) - H(X, Y)$


**样本熵**
- S：训练例子的样本集
- $p_{pos}$：是S中的正例比例
- $p_{neg}$：中反例的比例
- 用熵来测量S的混杂度

$$H(S) = -p_{pos}\log_2p_{pos} -p_{neg}\log_2p_{neg}$$

> 熵计算
> 例：
> ![](https://img-blog.csdnimg.cn/20201113110309967.png#pic_center)
### 信息增益
信息增益 = 熵 - 条件熵
$${GAIN}_{split} = Entropy(p) - \Sigma^k_{i=1}\frac{n_i}{n}Entropy(i)$$

> 通过Y获得X的信息，即降低X的不确定性
> 在一个条件下，信息复杂度（不确定性）减少的程度


## 评价
优点
- 构建过程计算资源开销小
- 分类未知样本速度极快
- 对于小规模的树比较容易解释
- 在许多小的简单数据集合上性能与其它方法相近

算法：C4.5（ID3）
- 深度优先构建方法
- 采用了信息增益
- 在每一个结点需要对示例依据连续属性排序
- 数据需要全部装入内存
- 不适合大规模数据

## 欠拟合、过拟合
![](https://img-blog.csdnimg.cn/20201113154250293.png#pic_center)