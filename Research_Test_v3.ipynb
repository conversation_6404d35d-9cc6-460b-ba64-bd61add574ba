import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel

tokenizer = AutoTokenizer.from_pretrained("nomic-ai/nomic-embed-code")
model = AutoModel.from_pretrained("nomic-ai/nomic-embed-code")

def last_token_pooling(hidden_states, attention_mask):
    sequence_lengths = attention_mask.sum(-1) - 1
    return hidden_states[torch.arange(hidden_states.shape[0]), sequence_lengths]

queries = ['Represent this query for searching relevant code: Calculate the n-th factorial']
codes = ['def fact(n):\n if n < 0:\n  raise ValueError\n return 1 if n == 0 else n * fact(n - 1)']
code_snippets = queries + codes

encoded_input = tokenizer(code_snippets, padding=True, truncation=True, return_tensors='pt')
model.eval()
with torch.no_grad():
    model_output = model(**encoded_input)[0]

embeddings = last_token_pooling(model_output, encoded_input['attention_mask'])
embeddings = F.normalize(embeddings, p=2, dim=1)
print(embeddings.shape)

similarity = F.cosine_similarity(embeddings[0], embeddings[1], dim=0)
print(similarity)


import requests
import base64
import os

api_host = "https://api.stability.ai"
api_key = "sk-V6tO1O2gqoLGvE1MK7kWB0kyGfgaRkcuSvl7KId7MxEM75Qx"

response = requests.post(
    f"{api_host}/v1/generation/stable-diffusion-v1-5/text-to-image",
    headers={
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {api_key}"
    },
    json={
        "text_prompts": [{"text": "a photo of an astronaut riding a horse on mars"}],
        "cfg_scale": 7,
        "height": 512,
        "width": 512,
        "samples": 1,
        "steps": 30,
        "intermediate_outputs": True,  # 启用中间输出
        "output_interval": 5  # 每5步返回一次结果
    },
)

# start a new terminal
import os
from IPython.display import IFrame

# Get DLAI_LOCAL_URL with a fallback value
base_url = os.environ.get('DLAI_LOCAL_URL')
if base_url is None:
    # Fallback to a common Jupyter local URL pattern
    base_url = "http://localhost:{port}/"

IFrame(f"{base_url.format(port=8888)}terminals/1", 
       width=600, height=768)



import anthropic

client = anthropic.Anthropic()

response = client.messages.create(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    thinking={
        "type": "enabled",
        "budget_tokens": 10000
    },
    messages=[{
        "role": "user",
        "content": "Are there an infinite number of prime numbers such that n mod 4 == 3?"
    }]
)

# The response will contain summarized thinking blocks and text blocks
for block in response.content:
    if block.type == "thinking":
        print(f"\nThinking summary: {block.thinking}")
    elif block.type == "text":
        print(f"\nResponse: {block.text}")

import anthropic

client = anthropic.Anthropic()

with client.messages.stream(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    thinking={"type": "enabled", "budget_tokens": 10000},
    messages=[{"role": "user", "content": "What is 27 * 453?"}],
) as stream:
    thinking_started = False
    response_started = False

    for event in stream:
        if event.type == "content_block_start":
            print(f"\nStarting {event.content_block.type} block...")
            # Reset flags for each new block
            thinking_started = False
            response_started = False
        elif event.type == "content_block_delta":
            if event.delta.type == "thinking_delta":
                if not thinking_started:
                    print("Thinking: ", end="", flush=True)
                    thinking_started = True
                print(event.delta.thinking, end="", flush=True)
            elif event.delta.type == "text_delta":
                if not response_started:
                    print("Response: ", end="", flush=True)
                    response_started = True
                print(event.delta.text, end="", flush=True)
        elif event.type == "content_block_stop":
            print("\nBlock complete.")

import anthropic

client = anthropic.Anthropic()

# Same tool definitions as before
calculator_tool = {
    "name": "calculator",
    "description": "Perform mathematical calculations",
    "input_schema": {
        "type": "object",
        "properties": {
            "expression": {
                "type": "string",
                "description": "Mathematical expression to evaluate"
            }
        },
        "required": ["expression"]
    }
}

database_tool = {
    "name": "database_query",
    "description": "Query product database",
    "input_schema": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "SQL query to execute"
            }
        },
        "required": ["query"]
    }
}

# First request with interleaved thinking enabled
response = client.messages.create(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    thinking={
        "type": "enabled",
        "budget_tokens": 10000
    },
    tools=[calculator_tool, database_tool],
    # Enable interleaved thinking with beta header
    extra_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14"
    },
    messages=[{
        "role": "user",
        "content": "What's the total revenue if we sold 150 units of product A at $50 each, and how does this compare to our average monthly revenue from the database?"
    }]
)

print("Initial response:")
thinking_blocks = []
tool_use_blocks = []

for block in response.content:
    if block.type == "thinking":
        thinking_blocks.append(block)
        print(f"Thinking: {block.thinking}")
    elif block.type == "tool_use":
        tool_use_blocks.append(block)
        print(f"Tool use: {block.name} with input {block.input}")
    elif block.type == "text":
        print(f"Text: {block.text}")

# First tool result (calculator)
calculator_result = "7500"  # 150 * 50

# Continue with first tool result
response2 = client.messages.create(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    thinking={
        "type": "enabled",
        "budget_tokens": 10000
    },
    tools=[calculator_tool, database_tool],
    extra_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14"
    },
    messages=[
        {
            "role": "user",
            "content": "What's the total revenue if we sold 150 units of product A at $50 each, and how does this compare to our average monthly revenue from the database?"
        },
        {
            "role": "assistant",
            "content": [thinking_blocks[0], tool_use_blocks[0]]
        },
        {
            "role": "user",
            "content": [{
                "type": "tool_result",
                "tool_use_id": tool_use_blocks[0].id,
                "content": calculator_result
            }]
        }
    ]
)

print("\nAfter calculator result:")
# With interleaved thinking, Claude can think about the calculator result
# before deciding to query the database
for block in response2.content:
    if block.type == "thinking":
        thinking_blocks.append(block)
        print(f"Interleaved thinking: {block.thinking}")
    elif block.type == "tool_use":
        tool_use_blocks.append(block)
        print(f"Tool use: {block.name} with input {block.input}")

# Second tool result (database)
database_result = "5200"  # Example average monthly revenue

# Continue with second tool result
response3 = client.messages.create(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    thinking={
        "type": "enabled",
        "budget_tokens": 10000
    },
    tools=[calculator_tool, database_tool],
    extra_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14"
    },
    messages=[
        {
            "role": "user",
            "content": "What's the total revenue if we sold 150 units of product A at $50 each, and how does this compare to our average monthly revenue from the database?"
        },
        {
            "role": "assistant",
            "content": [thinking_blocks[0], tool_use_blocks[0]]
        },
        {
            "role": "user",
            "content": [{
                "type": "tool_result",
                "tool_use_id": tool_use_blocks[0].id,
                "content": calculator_result
            }]
        },
        {
            "role": "assistant",
            "content": thinking_blocks[1:] + tool_use_blocks[1:]
        },
        {
            "role": "user",
            "content": [{
                "type": "tool_result",
                "tool_use_id": tool_use_blocks[1].id,
                "content": database_result
            }]
        }
    ]
)

print("\nAfter database result:")
# With interleaved thinking, Claude can think about both results
# before formulating the final response
for block in response3.content:
    if block.type == "thinking":
        print(f"Final thinking: {block.thinking}")
    elif block.type == "text":
        print(f"Final response: {block.text}")

import anthropic

client = anthropic.Anthropic(
    default_headers={
        "anthropic-beta": "code-execution-2025-05-22"
    }
)

response = client.messages.create(
    model="claude-opus-4-20250514",
    max_tokens=4096,
    messages=[{
        "role": "user",
        "content": "Calculate the mean and standard deviation of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
    }],
    tools=[{
        "type": "code_execution_20250522",
        "name": "code_execution"
    }]
)

print(response.content)

import anthropic
from termcolor import colored
import json
from IPython.display import display, HTML, Markdown

def format_claude_response(response):
    """Format Claude response content in a prettier way for Jupyter"""
    
    # Create HTML output for better formatting in Jupyter
    html_output = """
    <div style="border: 2px solid #00bcd4; border-radius: 10px; padding: 20px; margin: 10px 0; background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%); color: white; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
        <h2 style="color: #00bcd4; margin-top: 0; display: flex; align-items: center;">
            🤖 <span style="margin-left: 10px;">Claude Response</span>
        </h2>
    """
    
    for i, block in enumerate(response.content):
        if block.type == 'text' and block.text:
            html_output += f"""
            <div style="margin: 15px 0; padding: 15px; background: rgba(76, 175, 80, 0.1); border-left: 4px solid #4caf50; border-radius: 5px;">
                <h3 style="color: #4caf50; margin-top: 0; display: flex; align-items: center;">
                    📝 <span style="margin-left: 8px;">Text Response</span>
                </h3>
                <div style="white-space: pre-wrap; line-height: 1.6;">{block.text}</div>
            </div>
            """
            
        elif block.type == 'server_tool_use':
            html_output += f"""
            <div style="margin: 15px 0; padding: 15px; background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107; border-radius: 5px;">
                <h3 style="color: #ffc107; margin-top: 0; display: flex; align-items: center;">
                    🔧 <span style="margin-left: 8px;">Tool Used: {block.name}</span>
                </h3>
            """
            
            if 'code' in block.input:
                code = block.input['code'].replace('<', '&lt;').replace('>', '&gt;')
                html_output += f"""
                <div style="margin-top: 10px;">
                    <h4 style="color: #2196f3; margin: 10px 0; display: flex; align-items: center;">
                        💻 <span style="margin-left: 8px;">Code Executed:</span>
                    </h4>
                    <pre style="background: #1a1a1a; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #333;">
                        <code style="color: #e0e0e0; font-family: 'Courier New', monospace;">{code}</code>
                    </pre>
                </div>
                """
            html_output += "</div>"
                
        elif block.type == 'code_execution_tool_result':
            html_output += f"""
            <div style="margin: 15px 0; padding: 15px; background: rgba(156, 39, 176, 0.1); border-left: 4px solid #9c27b0; border-radius: 5px;">
                <h3 style="color: #9c27b0; margin-top: 0; display: flex; align-items: center;">
                    ✅ <span style="margin-left: 8px;">Execution Result</span>
                </h3>
            """
            
            if block.content.get('stdout'):
                stdout = block.content['stdout'].replace('<', '&lt;').replace('>', '&gt;')
                html_output += f"""
                <div style="margin: 10px 0;">
                    <h4 style="color: #4caf50; margin: 10px 0; display: flex; align-items: center;">
                        📊 <span style="margin-left: 8px;">Output:</span>
                    </h4>
                    <pre style="background: #0d4f3c; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #4caf50;">
                        <code style="color: #a5d6a7; font-family: 'Courier New', monospace;">{stdout}</code>
                    </pre>
                </div>
                """
                
            if block.content.get('stderr'):
                stderr = block.content['stderr'].replace('<', '&lt;').replace('>', '&gt;')
                html_output += f"""
                <div style="margin: 10px 0;">
                    <h4 style="color: #f44336; margin: 10px 0; display: flex; align-items: center;">
                        ⚠️ <span style="margin-left: 8px;">Errors:</span>
                    </h4>
                    <pre style="background: #4f1414; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #f44336;">
                        <code style="color: #ffcdd2; font-family: 'Courier New', monospace;">{stderr}</code>
                    </pre>
                </div>
                """
                
            return_code = block.content.get('return_code', 'N/A')
            color = '#4caf50' if return_code == 0 else '#f44336'
            html_output += f"""
            <div style="margin: 10px 0; padding: 8px; background: rgba(0, 188, 212, 0.1); border-radius: 5px;">
                <span style="color: {color}; font-weight: bold;">🔄 Return Code: {return_code}</span>
            </div>
            </div>
            """
    
    html_output += "</div>"
    
    # Display the formatted HTML
    display(HTML(html_output))
    
    # Also print colored terminal output for non-Jupyter environments
    print(colored("=" * 80, "cyan"))
    print(colored("🤖 Claude Response", "cyan", attrs=["bold"]))
    print(colored("=" * 80, "cyan"))
    
    for block in response.content:
        if block.type == 'text' and block.text:
            print(colored("\n📝 Text Response:", "green", attrs=["bold"]))
            print(colored("-" * 40, "green"))
            print(block.text)
                
        elif block.type == 'server_tool_use':
            print(colored(f"\n🔧 Tool Used: {block.name}", "yellow", attrs=["bold"]))
            print(colored("-" * 40, "yellow"))
            
            if 'code' in block.input:
                print(colored("💻 Code Executed:", "blue", attrs=["bold"]))
                print(colored("```python", "blue"))
                print(block.input['code'])
                print(colored("```", "blue"))
                
        elif block.type == 'code_execution_tool_result':
            print(colored("\n✅ Execution Result:", "magenta", attrs=["bold"]))
            print(colored("-" * 40, "magenta"))
            
            if block.content.get('stdout'):
                print(colored("📊 Output:", "green"))
                print(block.content['stdout'])
                
            if block.content.get('stderr'):
                print(colored("⚠️ Errors:", "red"))
                print(block.content['stderr'])
                
            print(colored(f"🔄 Return Code: {block.content.get('return_code', 'N/A')}", "cyan"))
    
    print(colored("\n" + "=" * 80, "cyan"))

# Use the pretty formatter instead of raw print
format_claude_response(response)



import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import make_blobs, load_iris, load_wine
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import adjusted_rand_score, silhouette_score
import pandas as pd

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


# Generate synthetic high-dimensional data
np.random.seed(42)
n_samples = 300
n_features = 10
n_centers = 4

# Create synthetic data with multiple clusters
X_synthetic, y_true = make_blobs(
    n_samples=n_samples, 
    centers=n_centers, 
    n_features=n_features,
    random_state=42,
    cluster_std=1.5
)

print(f"Synthetic data shape: {X_synthetic.shape}")
print(f"Number of true clusters: {n_centers}")
print(f"Features range: {X_synthetic.min():.2f} to {X_synthetic.max():.2f}")

# Standardize the data
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_synthetic)

print(f"Scaled data range: {X_scaled.min():.2f} to {X_scaled.max():.2f}")


# Apply PCA to reduce dimensionality
pca = PCA()
X_pca_full = pca.fit_transform(X_scaled)

# Calculate cumulative explained variance ratio
cumsum_var_ratio = np.cumsum(pca.explained_variance_ratio_)

# Plot explained variance
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.bar(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_)
plt.xlabel('Principal Component')
plt.ylabel('Explained Variance Ratio')
plt.title('Individual Explained Variance by PC')
plt.xticks(range(1, len(pca.explained_variance_ratio_) + 1))

plt.subplot(1, 2, 2)
plt.plot(range(1, len(cumsum_var_ratio) + 1), cumsum_var_ratio, 'bo-')
plt.axhline(y=0.95, color='r', linestyle='--', label='95% Variance')
plt.axhline(y=0.90, color='orange', linestyle='--', label='90% Variance')
plt.xlabel('Number of Components')
plt.ylabel('Cumulative Explained Variance Ratio')
plt.title('Cumulative Explained Variance')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Find number of components for 95% variance
n_components_95 = np.argmax(cumsum_var_ratio >= 0.95) + 1
print(f"Components needed for 95% variance: {n_components_95}")
print(f"Components needed for 90% variance: {np.argmax(cumsum_var_ratio >= 0.90) + 1}")

# Reduce to 2D for visualization
pca_2d = PCA(n_components=2)
X_pca_2d = pca_2d.fit_transform(X_scaled)
print(f"2D PCA explained variance: {pca_2d.explained_variance_ratio_.sum():.3f}")


# Determine optimal number of clusters using elbow method and silhouette score
def find_optimal_clusters(X, max_k=10):
    """Find optimal number of clusters using elbow method and silhouette score"""
    inertias = []
    silhouette_scores = []
    K_range = range(2, max_k + 1)
    
    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(X)
        inertias.append(kmeans.inertia_)
        silhouette_scores.append(silhouette_score(X, kmeans.labels_))
    
    return K_range, inertias, silhouette_scores

# Find optimal clusters for original high-dimensional data
K_range, inertias_orig, sil_scores_orig = find_optimal_clusters(X_scaled, max_k=8)

# Find optimal clusters for PCA-reduced data
K_range_pca, inertias_pca, sil_scores_pca = find_optimal_clusters(X_pca_2d, max_k=8)

# Plot results
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Original data - Elbow method
axes[0, 0].plot(K_range, inertias_orig, 'bo-')
axes[0, 0].set_xlabel('Number of Clusters (k)')
axes[0, 0].set_ylabel('Inertia')
axes[0, 0].set_title('Elbow Method - Original Data')
axes[0, 0].grid(True, alpha=0.3)

# Original data - Silhouette score
axes[0, 1].plot(K_range, sil_scores_orig, 'ro-')
axes[0, 1].set_xlabel('Number of Clusters (k)')
axes[0, 1].set_ylabel('Silhouette Score')
axes[0, 1].set_title('Silhouette Score - Original Data')
axes[0, 1].grid(True, alpha=0.3)

# PCA data - Elbow method
axes[1, 0].plot(K_range_pca, inertias_pca, 'go-')
axes[1, 0].set_xlabel('Number of Clusters (k)')
axes[1, 0].set_ylabel('Inertia')
axes[1, 0].set_title('Elbow Method - PCA Data')
axes[1, 0].grid(True, alpha=0.3)

# PCA data - Silhouette score
axes[1, 1].plot(K_range_pca, sil_scores_pca, 'mo-')
axes[1, 1].set_xlabel('Number of Clusters (k)')
axes[1, 1].set_ylabel('Silhouette Score')
axes[1, 1].set_title('Silhouette Score - PCA Data')
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Find best k based on silhouette score
best_k_orig = K_range[np.argmax(sil_scores_orig)]
best_k_pca = K_range_pca[np.argmax(sil_scores_pca)]

print(f"Best k for original data (silhouette): {best_k_orig}")
print(f"Best k for PCA data (silhouette): {best_k_pca}")
print(f"True number of clusters: {n_centers}")


# Perform clustering with optimal k
kmeans_orig = KMeans(n_clusters=n_centers, random_state=42, n_init=10)
kmeans_pca = KMeans(n_clusters=n_centers, random_state=42, n_init=10)

# Fit the models
labels_orig = kmeans_orig.fit_predict(X_scaled)
labels_pca = kmeans_pca.fit_predict(X_pca_2d)

# Calculate performance metrics
ari_orig = adjusted_rand_score(y_true, labels_orig)
ari_pca = adjusted_rand_score(y_true, labels_pca)
sil_orig = silhouette_score(X_scaled, labels_orig)
sil_pca = silhouette_score(X_pca_2d, labels_pca)

print("Clustering Performance Comparison:")
print("=" * 50)
print(f"Original High-Dimensional Data:")
print(f"  Adjusted Rand Index: {ari_orig:.3f}")
print(f"  Silhouette Score: {sil_orig:.3f}")
print(f"\nPCA-Reduced Data (2D):")
print(f"  Adjusted Rand Index: {ari_pca:.3f}")
print(f"  Silhouette Score: {sil_pca:.3f}")

# Visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# True clusters in PCA space
scatter1 = axes[0, 0].scatter(X_pca_2d[:, 0], X_pca_2d[:, 1], c=y_true, cmap='viridis', alpha=0.7)
axes[0, 0].set_title('True Clusters (PCA Space)')
axes[0, 0].set_xlabel('First Principal Component')
axes[0, 0].set_ylabel('Second Principal Component')
plt.colorbar(scatter1, ax=axes[0, 0])

# K-means on original data (visualized in PCA space)
scatter2 = axes[0, 1].scatter(X_pca_2d[:, 0], X_pca_2d[:, 1], c=labels_orig, cmap='viridis', alpha=0.7)
axes[0, 1].set_title(f'K-means on Original Data\\n(ARI: {ari_orig:.3f})')
axes[0, 1].set_xlabel('First Principal Component')
axes[0, 1].set_ylabel('Second Principal Component')
plt.colorbar(scatter2, ax=axes[0, 1])

# K-means on PCA data
scatter3 = axes[1, 0].scatter(X_pca_2d[:, 0], X_pca_2d[:, 1], c=labels_pca, cmap='viridis', alpha=0.7)
# Plot cluster centers
centers_pca = kmeans_pca.cluster_centers_
axes[1, 0].scatter(centers_pca[:, 0], centers_pca[:, 1], c='red', marker='x', s=200, linewidths=3)
axes[1, 0].set_title(f'K-means on PCA Data\\n(ARI: {ari_pca:.3f})')
axes[1, 0].set_xlabel('First Principal Component')
axes[1, 0].set_ylabel('Second Principal Component')
plt.colorbar(scatter3, ax=axes[1, 0])

# Comparison of cluster assignments
confusion_matrix = pd.crosstab(y_true, labels_pca, rownames=['True'], colnames=['Predicted'])
sns.heatmap(confusion_matrix, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1])
axes[1, 1].set_title('Confusion Matrix\\n(True vs PCA K-means)')

plt.tight_layout()
plt.show()


# Load wine dataset
wine_data = load_wine()
X_wine = wine_data.data
y_wine = wine_data.target
feature_names = wine_data.feature_names

print(f"Wine dataset shape: {X_wine.shape}")
print(f"Number of classes: {len(np.unique(y_wine))}")
print(f"Class distribution: {np.bincount(y_wine)}")
print(f"Feature names: {feature_names[:5]}...")  # Show first 5 features

# Standardize the wine data
scaler_wine = StandardScaler()
X_wine_scaled = scaler_wine.fit_transform(X_wine)

# Apply PCA to wine data
pca_wine = PCA()
X_wine_pca_full = pca_wine.fit_transform(X_wine_scaled)

# Plot explained variance for wine data
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.bar(range(1, len(pca_wine.explained_variance_ratio_) + 1), pca_wine.explained_variance_ratio_)
plt.xlabel('Principal Component')
plt.ylabel('Explained Variance Ratio')
plt.title('Wine Dataset - Individual Explained Variance')
plt.xticks(range(1, len(pca_wine.explained_variance_ratio_) + 1, 2))

plt.subplot(1, 2, 2)
cumsum_var_wine = np.cumsum(pca_wine.explained_variance_ratio_)
plt.plot(range(1, len(cumsum_var_wine) + 1), cumsum_var_wine, 'bo-')
plt.axhline(y=0.95, color='r', linestyle='--', label='95% Variance')
plt.axhline(y=0.90, color='orange', linestyle='--', label='90% Variance')
plt.xlabel('Number of Components')
plt.ylabel('Cumulative Explained Variance Ratio')
plt.title('Wine Dataset - Cumulative Explained Variance')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Find components needed for 95% variance
n_comp_95_wine = np.argmax(cumsum_var_wine >= 0.95) + 1
print(f"Components needed for 95% variance: {n_comp_95_wine}")

# Reduce to 2D for visualization
pca_wine_2d = PCA(n_components=2)
X_wine_pca_2d = pca_wine_2d.fit_transform(X_wine_scaled)
print(f"2D PCA explained variance: {pca_wine_2d.explained_variance_ratio_.sum():.3f}")


# Apply K-means clustering to wine data
kmeans_wine_orig = KMeans(n_clusters=3, random_state=42, n_init=10)
kmeans_wine_pca = KMeans(n_clusters=3, random_state=42, n_init=10)

# Fit the models
labels_wine_orig = kmeans_wine_orig.fit_predict(X_wine_scaled)
labels_wine_pca = kmeans_wine_pca.fit_predict(X_wine_pca_2d)

# Calculate performance metrics
ari_wine_orig = adjusted_rand_score(y_wine, labels_wine_orig)
ari_wine_pca = adjusted_rand_score(y_wine, labels_wine_pca)
sil_wine_orig = silhouette_score(X_wine_scaled, labels_wine_orig)
sil_wine_pca = silhouette_score(X_wine_pca_2d, labels_wine_pca)

print("Wine Dataset Clustering Results:")
print("=" * 50)
print(f"Original High-Dimensional Data (13 features):")
print(f"  Adjusted Rand Index: {ari_wine_orig:.3f}")
print(f"  Silhouette Score: {sil_wine_orig:.3f}")
print(f"\nPCA-Reduced Data (2D):")
print(f"  Adjusted Rand Index: {ari_wine_pca:.3f}")
print(f"  Silhouette Score: {sil_wine_pca:.3f}")

# Create comprehensive visualization
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# True wine classes in PCA space
scatter1 = axes[0, 0].scatter(X_wine_pca_2d[:, 0], X_wine_pca_2d[:, 1], c=y_wine, cmap='Set1', alpha=0.7)
axes[0, 0].set_title('True Wine Classes\\n(PCA Space)')
axes[0, 0].set_xlabel('First Principal Component')
axes[0, 0].set_ylabel('Second Principal Component')
plt.colorbar(scatter1, ax=axes[0, 0])

# K-means on original data (visualized in PCA space)
scatter2 = axes[0, 1].scatter(X_wine_pca_2d[:, 0], X_wine_pca_2d[:, 1], c=labels_wine_orig, cmap='Set1', alpha=0.7)
axes[0, 1].set_title(f'K-means on Original Data\\n(ARI: {ari_wine_orig:.3f})')
axes[0, 1].set_xlabel('First Principal Component')
axes[0, 1].set_ylabel('Second Principal Component')
plt.colorbar(scatter2, ax=axes[0, 1])

# K-means on PCA data
scatter3 = axes[0, 2].scatter(X_wine_pca_2d[:, 0], X_wine_pca_2d[:, 1], c=labels_wine_pca, cmap='Set1', alpha=0.7)
centers_wine_pca = kmeans_wine_pca.cluster_centers_
axes[0, 2].scatter(centers_wine_pca[:, 0], centers_wine_pca[:, 1], c='black', marker='x', s=200, linewidths=3)
axes[0, 2].set_title(f'K-means on PCA Data\\n(ARI: {ari_wine_pca:.3f})')
axes[0, 2].set_xlabel('First Principal Component')
axes[0, 2].set_ylabel('Second Principal Component')
plt.colorbar(scatter3, ax=axes[0, 2])

# Confusion matrices
conf_matrix_orig = pd.crosstab(y_wine, labels_wine_orig, rownames=['True'], colnames=['Predicted'])
sns.heatmap(conf_matrix_orig, annot=True, fmt='d', cmap='Blues', ax=axes[1, 0])
axes[1, 0].set_title('Confusion Matrix\\n(True vs Original K-means)')

conf_matrix_pca = pd.crosstab(y_wine, labels_wine_pca, rownames=['True'], colnames=['Predicted'])
sns.heatmap(conf_matrix_pca, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1])
axes[1, 1].set_title('Confusion Matrix\\n(True vs PCA K-means)')

# Feature importance (PCA loadings)
feature_importance = pd.DataFrame(
    pca_wine_2d.components_.T,
    columns=['PC1', 'PC2'],
    index=feature_names
)
feature_importance['Magnitude'] = np.sqrt(feature_importance['PC1']**2 + feature_importance['PC2']**2)
feature_importance_sorted = feature_importance.sort_values('Magnitude', ascending=True)

axes[1, 2].barh(range(len(feature_importance_sorted)), feature_importance_sorted['Magnitude'])
axes[1, 2].set_yticks(range(len(feature_importance_sorted)))
axes[1, 2].set_yticklabels(feature_importance_sorted.index, fontsize=8)
axes[1, 2].set_xlabel('Feature Importance (PCA Loading Magnitude)')
axes[1, 2].set_title('Feature Importance in PCA')

plt.tight_layout()
plt.show()

# Print top contributing features
print(f"\\nTop 5 features contributing to PCA components:")
print(feature_importance_sorted.tail().round(3))


# Create a comprehensive summary
def create_summary_table():
    """Create a summary table comparing different approaches"""
    
    summary_data = {
        'Dataset': ['Synthetic (10D)', 'Synthetic (10D)', 'Wine (13D)', 'Wine (13D)'],
        'Method': ['K-means Original', 'K-means + PCA', 'K-means Original', 'K-means + PCA'],
        'Dimensions': ['10', '2', '13', '2'],
        'ARI Score': [f'{ari_orig:.3f}', f'{ari_pca:.3f}', f'{ari_wine_orig:.3f}', f'{ari_wine_pca:.3f}'],
        'Silhouette Score': [f'{sil_orig:.3f}', f'{sil_pca:.3f}', f'{sil_wine_orig:.3f}', f'{sil_wine_pca:.3f}'],
        'Variance Explained': ['100%', f'{pca_2d.explained_variance_ratio_.sum():.1%}', 
                              '100%', f'{pca_wine_2d.explained_variance_ratio_.sum():.1%}']
    }
    
    return pd.DataFrame(summary_data)

summary_df = create_summary_table()
print("Performance Summary:")
print("=" * 80)
print(summary_df.to_string(index=False))

print("\\n\\nKey Insights:")
print("=" * 50)
print("1. PCA降维效果:")
print(f"   - 合成数据: 2个主成分解释了 {pca_2d.explained_variance_ratio_.sum():.1%} 的方差")
print(f"   - 酒类数据: 2个主成分解释了 {pca_wine_2d.explained_variance_ratio_.sum():.1%} 的方差")

print("\\n2. 聚类性能对比:")
if ari_pca > ari_orig:
    print("   - PCA降维后的K-means在合成数据上表现更好")
else:
    print("   - 原始高维数据的K-means在合成数据上表现更好")
    
if ari_wine_pca > ari_wine_orig:
    print("   - PCA降维后的K-means在酒类数据上表现更好")
else:
    print("   - 原始高维数据的K-means在酒类数据上表现更好")

print("\\n3. 维度诅咒的影响:")
print("   - 高维空间中的距离度量可能失效")
print("   - PCA可以去除噪声和冗余特征")
print("   - 降维有助于可视化和理解数据结构")

print("\\n4. 实际应用建议:")
print("   - 对于高维数据，建议先进行PCA分析")
print("   - 选择能解释90-95%方差的主成分数量")
print("   - 结合多种评估指标（ARI, Silhouette等）")
print("   - 考虑数据的实际含义和业务需求")


import torch
import torch.nn as nn

# Import torchvista
from torchvista import trace_model

# Define your module
class LinearModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 5)

    def forward(self, x):
        return self.linear(x)

# Instantiate the module and tensor input
model = LinearModel()
example_input = torch.randn(2, 10)

# Trace!
trace_model(model, example_input)

# # 显式显示图像
# plt.show()

# 或者保存图像
# plt.savefig('model_trace.png')



import matplotlib.pyplot as plt
import numpy as np

# 测试matplotlib是否工作
plt.figure()
plt.plot([1, 2, 3, 4])
plt.title('Test Plot')
plt.show()

import torch
from torchvista import trace_model
from torchvision.models.detection import fasterrcnn_mobilenet_v3_large_320_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor

model = fasterrcnn_mobilenet_v3_large_320_fpn(pretrained=True)
dummy_image = torch.rand(3, 320, 320)  # Shape: [C, H, W]
dummy_input = [dummy_image]

model.eval()

trace_model(model, dummy_input, show_non_gradient_nodes=False)



from openai import OpenAI

# 定义 Ollama 的 OpenAI 兼容 API 端点和占位符 API 密钥
OLLAMA_BASE_URL = "http://************:8234/v1"
OLLAMA_API_KEY = "ollama" # 必填 ，但 Ollama 会忽略此值

# 指定您要使用的本地 Ollama 模型
OLLAMA_MODEL = "gemma3n" # 或者您拉取的具体模型版本，例如 "gemma3n:4b"

try:
    # 初始化 OpenAI 客户端，将其指向 Ollama 服务器
    client = OpenAI(
        base_url=OLLAMA_BASE_URL,
        api_key=OLLAMA_API_KEY,
    )

    print(f"正在通过 OpenAI 兼容层向 Ollama 模型: {OLLAMA_MODEL} 发送请求...")

    # 发送标准的聊天补全请求
    chat_completion = client.chat.completions.create(
        model=OLLAMA_MODEL,  # 使用您的本地 Ollama 模型名称
        messages=[
            {"role": "system", "content": "你是一个乐于助人的助手。"},
            {"role": "user", "content": "解释一下 Ollama 和 llama.cpp 之间的区别。"}
        ]
    )

    print("模型响应:")
    print(chat_completion.choices[0].message.content)

except Exception as e:
    print(f"发生错误: {e}")
    print("请确保 Ollama 服务正在运行，并且您已拉取了指定的模型。")



from openai import OpenAI
import base64
import requests
from pathlib import Path

class OpenAIImageDescriber:
    def __init__(self, model: str = "gemma3n:e4b"):
        self.client = OpenAI(
            base_url="http://************:8234/v1",
            api_key="ollama"
        )
        self.model = model
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """Convert image to base64 for API"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def describe_image_stream(self, image_path: str, prompt: str = "What's in this image?"):
        """Stream image description using OpenAI-compatible API"""
        
        # Encode image
        base64_image = self.encode_image_to_base64(image_path)
        
        try:
            # Create the message with image
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            # Stream the response
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True,
                max_tokens=1000
            )
            
            print(f"Analyzing image: {image_path}\n")
            print("Response: ", end="", flush=True)
            
            full_response = ""
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
            
            print("\n")
            return full_response
            
        except Exception as e:
            print(f"Error: {e}")
            print("Note: OpenAI-compatible API might have limited image support.")
            print("Consider using the native Ollama client instead.")
            return None



# Usage

describer = OpenAIImageDescriber("gemma3n")
describer.describe_image_stream("/opt/app/researcher/ai-course/diloco_training_loss.png")

import ollama
from pathlib import Path
import base64
import asyncio
from typing import Union

class ImageDescriber:
    def __init__(self, model: str = "gemma3n", base_url: str = None):
        """Initialize with a vision-capable gemma3n model"""
        self.model = model
        # Set custom base_url or use default
        self.base_url = base_url or "http://localhost:11434"
        
    def encode_image_to_base64(self, image_path: str) -> str:
        """Convert image file to base64 string"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def describe_image_stream(self, image_path: str, prompt: str = "Describe this image in detail"):
        """Stream image description using native Ollama client"""
        
        # Create client with custom base_url
        client = ollama.Client(host=self.base_url)
        
        # Stream the response
        stream = client.chat(
            model=self.model,
            messages=[{
                'role': 'user',
                'content': prompt,
                'images': [image_path]  # Can use path directly
            }],
            stream=True
        )
        
        print(f"Analyzing image: {image_path}\n")
        print(f"Using Ollama at: {self.base_url}\n")
        print("Response: ", end="", flush=True)
        
        full_response = ""
        for chunk in stream:
            if 'message' in chunk and 'content' in chunk['message']:
                content = chunk['message']['content']
                print(content, end="", flush=True)
                full_response += content
        
        print("\n")
        return full_response
    
    async def describe_image_async(self, image_path: str, prompt: str = "What do you see in this image?"):
        """Async version with streaming"""
        # Create async client with custom base_url
        client = ollama.AsyncClient(host=self.base_url)
        
        # Convert image to base64
        image_base64 = self.encode_image_to_base64(image_path)
        
        stream = await client.chat(
            model=self.model,
            messages=[{
                'role': 'user',
                'content': prompt,
                'images': [image_base64]  # Using base64 encoding
            }],
            stream=True
        )
        
        print(f"Async analysis of: {image_path}\n")
        print(f"Using Ollama at: {self.base_url}\n")
        full_response = ""
        
        async for chunk in stream:
            if 'message' in chunk and 'content' in chunk['message']:
                content = chunk['message']['content']
                print(content, end="", flush=True)
                full_response += content
        
        print("\n")
        return full_response



# Custom local port
describer = ImageDescriber("gemma3n", base_url="http://************:8234")
describer.describe_image_stream("/opt/app/researcher/ai-course/diloco_training_loss.png")

    