#ifndef RAW_STATUS
#define RAW_STATUS(...)
#endif

#ifndef STATUS
#define STATUS(...)
#endif

#define COMMON_STATUS(...) RAW_STATUS(__VA_ARGS__)
#define TRANSACTION_STATUS(...) STATUS(Transaction, __VA_ARGS__)
#define RPC_STATUS(...) STATUS(RPC, __VA_ARGS__)
#define META_STATUS(...) STATUS(Meta, __VA_ARGS__)
#define STORAGE_STATUS(...) STATUS(Storage, __VA_ARGS__)
#define STORAGE_CLIENT_STATUS(...) STATUS(StorageClient, __VA_ARGS__)
#define MGMTD_STATUS(...) STATUS(Mgmtd, __VA_ARGS__)
#define MGMTD_CLIENT_STATUS(...) STATUS(MgmtdClient, __VA_ARGS__)
#define CLIENT_AGENT_STATUS(...) STATUS(ClientAgent, __VA_ARGS__)
#define CLI_STATUS(...) STATUS(Cli, __VA_ARGS__)
#define KV_SERVICE_STATUS(...) STATUS(KvService, __VA_ARGS__)

COMMON_STATUS(OK, 0)
COMMON_STATUS(NotImplemented, 1)
COMMON_STATUS(DataCorruption, 2)
COMMON_STATUS(InvalidArg, 3)
COMMON_STATUS(InvalidConfig, 4)
COMMON_STATUS(QueueEmpty, 5)
COMMON_STATUS(QueueFull, 6)
COMMON_STATUS(QueueConflict, 7)
COMMON_STATUS(QueueInvalidItem, 8)
COMMON_STATUS(MonitorInitFailed, 12)
COMMON_STATUS(MonitorQueryFailed, 13)
COMMON_STATUS(MonitorWriteFailed, 14)
COMMON_STATUS(ConfigInvalidType, 15)
COMMON_STATUS(ConfigInvalidValue, 16)
COMMON_STATUS(ConfigUpdateFailed, 17)
COMMON_STATUS(ConfigValidateFailed, 18)
COMMON_STATUS(ConfigRedundantKey, 19)
COMMON_STATUS(ConfigKeyNotFound, 20)
COMMON_STATUS(AuthenticationFail, 25)
COMMON_STATUS(NotEnoughMemory, 26)
COMMON_STATUS(Interrupted, 27)
COMMON_STATUS(InvalidFormat, 33)
COMMON_STATUS(ReadOnlyMode, 34)
COMMON_STATUS(SerdeInsufficientLength, 40)
COMMON_STATUS(SerdeMissingFieldsAtEnd, 41)
COMMON_STATUS(SerdeVariantIndexExceeded, 42)
COMMON_STATUS(SerdeUnknownEnumValue, 43)
COMMON_STATUS(SerdeNotContainer, 44)
COMMON_STATUS(SerdeNotString, 45)
COMMON_STATUS(SerdeNotNumber, 46)
COMMON_STATUS(SerdeNotInteger, 47)
COMMON_STATUS(SerdeNotTable, 48)
COMMON_STATUS(SerdeKeyNotFound, 49)
COMMON_STATUS(SerdeInvalidJson, 50)
COMMON_STATUS(SerdeInvalidToml, 51)
COMMON_STATUS(NoApplication, 52)
COMMON_STATUS(CannotPushConfig, 53)
COMMON_STATUS(KVStoreNotFound, 60)
COMMON_STATUS(KVStoreGetError, 61)
COMMON_STATUS(KVStoreSetError, 62)
COMMON_STATUS(KVStoreOpenFailed, 63)
COMMON_STATUS(TokenMismatch, 64)
COMMON_STATUS(TokenDuplicated, 65)
COMMON_STATUS(TokenStale, 66)
COMMON_STATUS(TooManyTokens, 67)
COMMON_STATUS(KVStoreIterateError, 68)
COMMON_STATUS(IOError, 69)
COMMON_STATUS(FaultInjection, 70)
COMMON_STATUS(ConfigParseError, 71)
COMMON_STATUS(OSError, 72)
COMMON_STATUS(FoundBug, 998)
COMMON_STATUS(Unknown, 999)

/* 1xxx: transaction errors. */
TRANSACTION_STATUS(Failed, 1000)
TRANSACTION_STATUS(Conflict, 1001)
TRANSACTION_STATUS(Throttled, 1002)
TRANSACTION_STATUS(TooOld, 1003)
TRANSACTION_STATUS(NetworkError, 1004)
TRANSACTION_STATUS(Canceled, 1005)
TRANSACTION_STATUS(MaybeCommitted, 1006)
TRANSACTION_STATUS(Retryable, 1007)
TRANSACTION_STATUS(ResourceConstrained, 1008)
TRANSACTION_STATUS(ProcessBehind, 1009)
TRANSACTION_STATUS(FutureVersion, 1010)

/* 2xxx: RPC errors. */
RPC_STATUS(InvalidMessageType, 2000)
RPC_STATUS(RequestIsEmpty, 2001)
RPC_STATUS(VerifyRequestFailed, 2002)
RPC_STATUS(VerifyResponseFailed, 2003)
RPC_STATUS(Timeout, 2005)
RPC_STATUS(InvalidAddr, 2006)
RPC_STATUS(SendFailed, 2007)
RPC_STATUS(InvalidServiceID, 2008)
RPC_STATUS(InvalidMethodID, 2009)
RPC_STATUS(SocketError, 2010)
RPC_STATUS(ListenFailed, 2011)
RPC_STATUS(RequestRefused, 2012)
RPC_STATUS(SocketClosed, 2013)
RPC_STATUS(ConnectFailed, 2014)
RPC_STATUS(IBInitFailed, 2015)
RPC_STATUS(IBDeviceNotFound, 2016)
RPC_STATUS(RDMAPostFailed, 2017)
RPC_STATUS(RDMAError, 2018)
RPC_STATUS(RDMANoBuf, 2019)
RPC_STATUS(InvalidServiceName, 2020)
RPC_STATUS(IBDeviceNotInitialized, 2021)
RPC_STATUS(EpollInitError, 2022)
RPC_STATUS(EpollAddError, 2023)
RPC_STATUS(EpollDelError, 2024)
RPC_STATUS(EpollWakeUpError, 2025)
RPC_STATUS(EpollWaitError, 2026)
RPC_STATUS(IBOpenPortFailed, 2027)

/* 3xxx: meta errors. */
// NOTE: update ErrorHandling in fbs/meta/Utils.h after add new error codes.
META_STATUS(NotFound, 3000)
META_STATUS(NotEmpty, 3001)
META_STATUS(NotDirectory, 3003)
META_STATUS(TooManySymlinks, 3005)
META_STATUS(IsDirectory, 3006)
META_STATUS(Exists, 3007)
META_STATUS(NoPermission, 3008)
META_STATUS(Inconsistent, 3009)
META_STATUS(NotFile, 3010)
META_STATUS(BadFileSystem, 3011)
META_STATUS(InodeIdAllocFailed, 3012)
META_STATUS(InvalidFileLayout, 3013)
META_STATUS(FileHasHole, 3014)
META_STATUS(OTruncFailed, 3015)
META_STATUS(MoreChunksToRemove, 3016)
META_STATUS(NameTooLong, 3017)
META_STATUS(RequestCanceled, 3018)
META_STATUS(Busy, 3019)
// NOTE: add expected error code under Expected
META_STATUS(Expected, 3100)
META_STATUS(NoLock, 3101)
META_STATUS(FileTooLarge, 3102)
// NOTE: add retryable error code under Retryable
META_STATUS(Retryable, 3200)
META_STATUS(ForwardFailed, 3201)
META_STATUS(ForwardTimeout, 3202)
META_STATUS(OperationTimeout, 3203)
// NOTE: add not retryable error under NotRetryable
META_STATUS(NotRetryable, 3300)
META_STATUS(FoundBug, 3999)

/* 4xxx: storage errors. */
STORAGE_STATUS(ChunkMetadataUnpackError, 4000)
STORAGE_STATUS(ChunkMetadataNotFound, 4001)
STORAGE_STATUS(ChunkMetadataGetError, 4002)
STORAGE_STATUS(ChunkMetadataSetError, 4003)
STORAGE_STATUS(ChunkNotCommit, 4004)
STORAGE_STATUS(ChunkNotClean, 4005)
STORAGE_STATUS(ChunkStaleUpdate, 4006)
STORAGE_STATUS(ChunkMissingUpdate, 4007)
STORAGE_STATUS(ChunkCommittedUpdate, 4008)
STORAGE_STATUS(ChunkOpenFailed, 4009)
STORAGE_STATUS(ChunkReadFailed, 4010)
STORAGE_STATUS(ChunkWriteFailed, 4011)
STORAGE_STATUS(ChunkAdvanceUpdate, 4012)
STORAGE_STATUS(ChunkApplyFailed, 4013)
STORAGE_STATUS(PunchHoleFailed, 4014)
STORAGE_STATUS(ChunkSizeMismatch, 4015)
STORAGE_STATUS(StorageStatFailed, 4016)
STORAGE_STATUS(StorageUUIDMismatch, 4017)
STORAGE_STATUS(StorageInitFailed, 4018)
STORAGE_STATUS(MetaStoreOpenFailed, 4020)
STORAGE_STATUS(MetaStoreInvalidIterator, 4021)
STORAGE_STATUS(ChunkNotReadyToRemove, 4022)
STORAGE_STATUS(ChunkStaleCommit, 4023)
STORAGE_STATUS(ChunkInvalidChunkSize, 4024)
STORAGE_STATUS(TargetOffline, 4030)
STORAGE_STATUS(TargetNotFound, 4031)
STORAGE_STATUS(TargetStateInvalid, 4032)
STORAGE_STATUS(NoSuccessorTarget, 4033)
STORAGE_STATUS(NoSuccessorAddr, 4034)
STORAGE_STATUS(ChunkStoreInitFailed, 4040)
STORAGE_STATUS(AioSubmitFailed, 4050)
STORAGE_STATUS(AioGetEventsFailed, 4051)
STORAGE_STATUS(BufferSizeExceeded, 4060)
STORAGE_STATUS(SyncStartFailed, 4070)
STORAGE_STATUS(SyncSendStartFailed, 4071)
STORAGE_STATUS(SyncSendDoneFailed, 4072)
STORAGE_STATUS(ChecksumMismatch, 4080)
STORAGE_STATUS(ChainVersionMismatch, 4081)
STORAGE_STATUS(ChunkVersionMismatch, 4082)
STORAGE_STATUS(ChannelIsLocked, 4090)
STORAGE_STATUS(ChunkIsLocked, 4091)

/* 5xxx: mgmtd errors. */
MGMTD_STATUS(NotPrimary, 5000)
MGMTD_STATUS(NodeNotFound, 5001)
MGMTD_STATUS(HeartbeatFail, 5002)
MGMTD_STATUS(ClusterIdMismatch, 5003)
MGMTD_STATUS(RegisterFail, 5004)
MGMTD_STATUS(InvalidRoutingInfoVersion, 5005)
MGMTD_STATUS(InvalidConfigVersion, 5006)
MGMTD_STATUS(InvalidChainTable, 5007)
MGMTD_STATUS(InvalidChainTableVersion, 5008)
MGMTD_STATUS(HeartbeatVersionStale, 5009)
MGMTD_STATUS(ClientSessionVersionStale, 5010)
MGMTD_STATUS(InvalidTag, 5011)
MGMTD_STATUS(ChainNotFound, 5012)
MGMTD_STATUS(NodeTypeMismatch, 5013)
MGMTD_STATUS(ExtendClientSessionMismatch, 5014)
MGMTD_STATUS(ClientSessionNotFound, 5015)
MGMTD_STATUS(NotAdmin, 5016)
MGMTD_STATUS(TargetNotFound, 5017)
MGMTD_STATUS(TargetExisted, 5018)

/* 6xxx: mgmtd client errors. */
MGMTD_CLIENT_STATUS(PrimaryMgmtdNotFound, 6000)
MGMTD_CLIENT_STATUS(WorkQueueFull, 6001)
MGMTD_CLIENT_STATUS(MetaServiceNotAvailable, 6002)
MGMTD_CLIENT_STATUS(Exit, 6003)  // internal used
MGMTD_CLIENT_STATUS(RoutingInfoNotReady, 6004)

/* 7xxx: storage client errors. */
STORAGE_CLIENT_STATUS(InitFailed, 7000)
STORAGE_CLIENT_STATUS(MemoryError, 7001)
STORAGE_CLIENT_STATUS(InvalidArg, 7002)
STORAGE_CLIENT_STATUS(NotInitialized, 7003)
STORAGE_CLIENT_STATUS(RoutingError, 7004)
STORAGE_CLIENT_STATUS(NotAvailable, 7005)
STORAGE_CLIENT_STATUS(CommError, 7006)
STORAGE_CLIENT_STATUS(ChunkNotFound, 7007)
STORAGE_CLIENT_STATUS(Timeout, 7008)
STORAGE_CLIENT_STATUS(BadConfig, 7009)
STORAGE_CLIENT_STATUS(RemoteIOError, 7010)
STORAGE_CLIENT_STATUS(ServerError, 7011)
STORAGE_CLIENT_STATUS(ResourceBusy, 7012)
STORAGE_CLIENT_STATUS(DuplicateUpdate, 7013)
STORAGE_CLIENT_STATUS(RoutingVersionMismatch, 7014)
STORAGE_CLIENT_STATUS(ChecksumMismatch, 7015)
STORAGE_CLIENT_STATUS(NoRDMAInterface, 7016)
STORAGE_CLIENT_STATUS(ProtocolMismatch, 7017)
STORAGE_CLIENT_STATUS(RequestCanceled, 7018)
STORAGE_CLIENT_STATUS(ReadOnlyServer, 7019)
STORAGE_CLIENT_STATUS(ChunkNotCommit, 7020)
STORAGE_CLIENT_STATUS(NoSpace, 7021)
STORAGE_CLIENT_STATUS(FoundBug, 7999)

/* 8xxx: client agent & fuse errors */
CLIENT_AGENT_STATUS(TooManyOpenFiles, 8000)
CLIENT_AGENT_STATUS(HoleInIoOutcome, 8001)
CLIENT_AGENT_STATUS(FailedToStart, 8002)
CLIENT_AGENT_STATUS(OperationDisabled, 8003)
CLIENT_AGENT_STATUS(IovNotRegistered, 8004)
CLIENT_AGENT_STATUS(IovShmFail, 8006)

/*10xxx: cli errors */
CLI_STATUS(WrongUsage, 10000)

/*11xxx: object storage errors */
KV_SERVICE_STATUS(UpdateConflict, 11000)
KV_SERVICE_STATUS(OperatingByOthers, 11001)
KV_SERVICE_STATUS(StoreNotFound, 11002)
KV_SERVICE_STATUS(StoreNotAvailable, 11003)
KV_SERVICE_STATUS(StoreLoaded, 11004)
KV_SERVICE_STATUS(ClusterIdMismatch, 11005)
KV_SERVICE_STATUS(NotPrimary, 11006)
KV_SERVICE_STATUS(TableInfoStale, 11007)
KV_SERVICE_STATUS(HeartbeatVersionStale, 11008)
KV_SERVICE_STATUS(UnknownWorker, 11009)
KV_SERVICE_STATUS(StoreLeaseEmpty, 11010)
KV_SERVICE_STATUS(StoreLeaseHeld, 11011)
KV_SERVICE_STATUS(TableNotFound, 11012)
KV_SERVICE_STATUS(TableIdMismatch, 11013)
KV_SERVICE_STATUS(NoAvailableWorker, 11014)
KV_SERVICE_STATUS(NoPermission, 11015)
KV_SERVICE_STATUS(NoAvailableMaster, 11016)
KV_SERVICE_STATUS(StoreTypeMismatch, 11017)
KV_SERVICE_STATUS(WriteStale, 11018)
KV_SERVICE_STATUS(Exists, 11019)
KV_SERVICE_STATUS(NotFound, 11020)
KV_SERVICE_STATUS(OutOfRange, 11021)
KV_SERVICE_STATUS(Deleted, 11022)
KV_SERVICE_STATUS(ServiceStopping, 11023)

#undef STATUS
#undef RAW_STATUS
